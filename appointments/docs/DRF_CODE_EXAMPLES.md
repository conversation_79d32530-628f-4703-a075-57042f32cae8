# Django REST Framework Code Examples - Appointments App

This document provides practical code examples from the appointments app to illustrate DRF implementation patterns.

## Table of Contents
1. [Serializer Examples](#serializer-examples)
2. [ViewSet Examples](#viewset-examples)
3. [Model Method Examples](#model-method-examples)
4. [Authentication Examples](#authentication-examples)
5. [Custom Action Examples](#custom-action-examples)
6. [Validation Examples](#validation-examples)

## Serializer Examples

### 1. Dynamic Serializer with Custom URL Masking

```python
class AppointmentSerializer(serializers.ModelSerializer):
    """Display serializer with custom URL masking for public APIs"""
    
    doctor_id = serializers.SerializerMethodField()
    patient_id = serializers.SerializerMethodField()
    attachments = AppointmentAttachmentSerializer(many=True, read_only=True)
    
    class Meta:
        model = Appointment
        fields = [
            'id', 'doctor_id', 'patient_id',
            'start_time', 'end_time', 'status', 'mode',
            'title', 'appointment_type', 'attachments',
            'created_at', 'updated_at', 'meeting_link', 'notes',
            'insurance', 'direct_payment', 'cancellation_reason'
        ]
        read_only_fields = [
            'id', 'doctor_id', 'patient_id',
            'created_at', 'updated_at', 'meeting_link'
        ]

    def get_doctor_id(self, obj):
        """Return custom_url_username for booking appointments, UUID for manual appointments"""
        if obj.appointment_type == 'booking' and obj.doctor:
            return obj.doctor.custom_url_username
        elif obj.doctor:
            return str(obj.doctor.id)
        return None

    def get_patient_id(self, obj):
        """Return custom_url_username for booking appointments, UUID for manual appointments"""
        if obj.appointment_type == 'booking' and obj.patient:
            return obj.patient.custom_url_username
        elif obj.patient:
            return str(obj.patient.id)
        return None
```

### 2. Complex Creation Serializer with User Auto-Creation

```python
class AppointmentCreateSerializer(serializers.ModelSerializer):
    """Handle appointment creation with complex validation and user auto-creation"""
    
    file_ids = serializers.ListField(
        child=serializers.CharField(),
        write_only=True,
        required=False,
        help_text="List of file IDs to attach to the appointment"
    )
    email = serializers.EmailField(write_only=True, required=False)
    
    class Meta:
        model = Appointment
        fields = '__all__'
        extra_kwargs = {
            'creator': {'required': False},
            'patient': {'required': False},
        }

    def create(self, validated_data):
        request = self.context.get('request')
        file_ids = validated_data.pop('file_ids', [])
        
        # Handle user creation for unauthenticated requests
        if request.user.is_authenticated:
            validated_data['creator'] = request.user
        else:
            email = validated_data.pop('email', None)
            if not email:
                raise serializers.ValidationError({
                    'error': 'Email required',
                    'detail': 'Email is required for unauthenticated users'
                })
            
            from accounts.models import CustomUser
            user, created = CustomUser.objects.get_or_create(
                email=email,
                defaults={'is_active': True}
            )
            validated_data['creator'] = user

        # Create appointment
        appointment = super().create(validated_data)
        
        # Process file attachments
        for file_id in file_ids:
            try:
                uploaded_file = UploadedFile.objects.get(id=file_id)
                AppointmentAttachment.objects.create(
                    appointment=appointment,
                    file=uploaded_file
                )
            except UploadedFile.DoesNotExist:
                continue
        
        return appointment
```

### 3. Validation Mixin for Availability

```python
class DoctorAvailabilityValidationMixin:
    """Mixin for availability validation logic"""
    
    def validate(self, data):
        # Validate recurrence pattern
        recurrence_type = data.get('recurrence_type', 'none')
        
        if recurrence_type == 'weekly':
            if not data.get('recurrence_days'):
                raise serializers.ValidationError({
                    'recurrence_days': 'Required for weekly recurrence'
                })
        
        elif recurrence_type == 'monthly':
            if not data.get('recurrence_month_day'):
                raise serializers.ValidationError({
                    'recurrence_month_day': 'Required for monthly recurrence'
                })
        
        # Validate time consistency
        start_time = data.get('start_time')
        end_time = data.get('end_time')
        
        if start_time and end_time and start_time >= end_time:
            raise serializers.ValidationError({
                'end_time': 'End time must be after start time'
            })
        
        return data

class DoctorAvailabilitySerializer(DoctorAvailabilityValidationMixin, serializers.ModelSerializer):
    doctor_name = serializers.CharField(source='doctor.get_full_name', read_only=True)

    class Meta:
        model = DoctorAvailability
        fields = [
            'id', 'doctor_id', 'doctor_name', 'clinic', 'enterprise', 'title',
            'start_date', 'end_date', 'start_time', 'end_time',
            'is_active', 'recurrence_type', 'recurrence_interval',
            'recurrence_days', 'recurrence_month_day',
            'recurrence_end_date', 'recurrence_count', 'mode', 'need_payment'
        ]
        read_only_fields = ['id', 'doctor_name']
```

## ViewSet Examples

### 1. Dynamic Permissions and Serializers

```python
class AppointmentViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = AppointmentCreateSerializer
    queryset = Appointment.objects.all()

    def get_serializer_class(self):
        """Dynamic serializer selection based on action"""
        if self.action in ['create']:
            return AppointmentCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return AppointmentUpdateSerializer
        elif self.action in ['list', 'retrieve']:
            return AppointmentSerializer
        return AppointmentCreateSerializer

    def get_permissions(self):
        """Dynamic permissions - public creation, authenticated for others"""
        if self.action == 'create':
            return [AllowAny()]
        return [IsAuthenticated()]

    def get_queryset(self):
        """Role-based queryset filtering with custom URL support"""
        queryset = Appointment.objects.select_related(
            'doctor', 'patient', 'creator'
        ).prefetch_related('attachments__file')
        
        # Filter by role
        role = self.request.query_params.get('role')
        if role == 'doctor':
            queryset = queryset.filter(doctor=self.request.user)
        elif role == 'patient':
            queryset = queryset.filter(patient=self.request.user)
        
        # Filter by status
        status = self.request.query_params.get('status')
        if status:
            queryset = queryset.filter(status=status)
        
        # Date range filtering
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date:
            queryset = queryset.filter(start_time__date__gte=start_date)
        if end_date:
            queryset = queryset.filter(end_time__date__lte=end_date)
        
        # Support custom URL username filtering
        doctor_identifier = self.request.query_params.get('doctor')
        if doctor_identifier:
            doctor = get_user_by_identifier_or_404(doctor_identifier)
            queryset = queryset.filter(doctor=doctor)
        
        return queryset
```

### 2. Custom Actions with Public Access

```python
class DoctorAvailabilityViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = DoctorAvailabilitySerializer
    queryset = DoctorAvailability.objects.all()

    def get_permissions(self):
        """Allow public access to available_slots"""
        if self.action == 'available_slots':
            return [AllowAny()]
        return [IsAuthenticated()]

    @action(detail=False, methods=['get'], permission_classes=[AllowAny])
    def available_slots(self, request):
        """Public endpoint for getting available appointment slots"""
        doctor_identifier = request.query_params.get('doctor')
        if not doctor_identifier:
            return Response({
                'error': 'Doctor identifier required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Resolve doctor by custom URL username or ID
        try:
            doctor = get_user_by_identifier_or_404(doctor_identifier)
        except CustomUser.DoesNotExist:
            return Response({
                'error': 'Doctor not found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Validate doctor role
        if not doctor.role or doctor.role.name != 'doctor':
            return Response({
                'error': 'User is not a doctor'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get date range
        start_date_str = request.query_params.get('start_date')
        end_date_str = request.query_params.get('end_date')
        
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        except (ValueError, TypeError):
            return Response({
                'error': 'Invalid date format. Use YYYY-MM-DD'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Calculate available slots
        available_slots = self._calculate_available_slots(doctor, start_date, end_date)
        
        return Response({
            'doctor': doctor.custom_url_username,
            'available_slots': available_slots
        })

    def _calculate_available_slots(self, doctor, start_date, end_date):
        """Calculate available slots for a doctor in date range"""
        slots_by_date = {}
        
        # Get doctor's availabilities
        availabilities = DoctorAvailability.objects.filter(
            doctor=doctor,
            is_active=True
        )
        
        # Generate slots for each availability
        for availability in availabilities:
            occurrences = availability.get_occurrences(start_date, end_date)
            
            for occurrence in occurrences:
                date_str = occurrence['date'].strftime('%Y-%m-%d')
                if date_str not in slots_by_date:
                    slots_by_date[date_str] = []
                
                # Check for conflicts with existing appointments
                conflicts = Appointment.objects.filter(
                    doctor=doctor,
                    start_time__date=occurrence['date'],
                    status__in=['pending', 'confirmed']
                )
                
                if not conflicts.exists():
                    slots_by_date[date_str].append({
                        'start_time': occurrence['start_time'].strftime('%H:%M:%S'),
                        'end_time': occurrence['end_time'].strftime('%H:%M:%S'),
                        'mode': availability.mode.split(','),
                        'need_payment': availability.need_payment
                    })
        
        return [
            {'date': date, 'slots': slots}
            for date, slots in sorted(slots_by_date.items())
        ]

    @action(detail=False, methods=['get'], permission_classes=[AllowAny])
    def by_doctor(self, request):
        """Get availability by doctor custom_url_username or ID"""
        doctor_identifier = request.query_params.get('doctor')
        if not doctor_identifier:
            return Response({
                'error': 'Doctor identifier required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        doctor = get_user_by_identifier_or_404(doctor_identifier)
        
        # Check if doctor role
        if not doctor.role or doctor.role.name != 'doctor':
            return Response({
                'error': 'User is not a doctor'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get availabilities with date filtering
        queryset = DoctorAvailability.objects.filter(
            doctor=doctor,
            is_active=True
        )
        
        # Apply date filters if provided
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        
        if start_date:
            queryset = queryset.filter(start_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(
                models.Q(end_date__lte=end_date) | models.Q(end_date__isnull=True)
            )
        
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
```

## Model Method Examples

### 1. Appointment Model Business Logic

```python
class Appointment(BaseModel):
    # ... field definitions ...

    def clean(self):
        """Model-level validation for appointment conflicts and business rules"""
        super().clean()

        # Validation for booking with doctor
        if self.appointment_type == 'booking':
            if not self.doctor:
                raise ValidationError("Doctor is required for booking appointments.")
            if not self.patient:
                raise ValidationError("Patient is required for booking appointments.")

            # Check availability conflicts
            self._validate_availability_conflict()

            # Validate payment requirements
            self._validate_payment_requirements()

    def _validate_availability_conflict(self):
        """Check for appointment conflicts with existing bookings"""
        conflicts = Appointment.objects.filter(
            doctor=self.doctor,
            start_time__lt=self.end_time,
            end_time__gt=self.start_time,
            status__in=['pending', 'confirmed']
        ).exclude(pk=self.pk)

        if conflicts.exists():
            raise ValidationError(
                f"Time slot conflicts with existing appointment: {conflicts.first()}"
            )

    def _validate_payment_requirements(self):
        """Validate payment requirements based on availability settings"""
        availability = self.get_related_availability()
        if availability and availability.need_payment:
            if not self.has_valid_payment_method():
                raise ValidationError(
                    "Payment method required for this appointment slot"
                )

    def get_related_availability(self):
        """Get the availability that was used for this appointment"""
        if self.appointment_type != 'booking' or not self.doctor:
            return None

        date = self.start_time.date()
        start_time = self.start_time.time()
        end_time = self.end_time.time()

        # Check overrides first
        override = DoctorAvailabilityOverride.objects.filter(
            doctor=self.doctor,
            date=date,
            is_active=True
        ).first()

        if override:
            return None  # Override doesn't have need_payment field

        # Check regular availabilities
        availabilities = DoctorAvailability.objects.filter(
            doctor=self.doctor,
            is_active=True
        ).filter(
            models.Q(
                # Non-recurring availabilities
                recurrence_type='none',
                start_date=date
            ) |
            models.Q(
                # Recurring availabilities
                ~models.Q(recurrence_type='none'),
                start_date__lte=date
            )
        )

        # Find the availability that contains this appointment time
        for availability in availabilities:
            occurrences = availability.get_occurrences(date, date)
            for occurrence in occurrences:
                if (occurrence['start_time'] <= start_time and
                    occurrence['end_time'] >= end_time):
                    return availability

        return None

    def has_valid_payment_method(self):
        """Check if appointment has valid payment method or is paid"""
        # Implementation depends on payment system integration
        return self.direct_payment or self.insurance
```

### 2. DoctorAvailability Recurrence Logic

```python
class DoctorAvailability(BaseModel):
    # ... field definitions ...

    def get_occurrences(self, start_date, end_date):
        """Generate occurrence dates based on recurrence pattern"""
        occurrences = []

        if self.recurrence_type == 'none':
            # Single occurrence
            if self.start_date and start_date <= self.start_date <= end_date:
                occurrences.append({
                    'date': self.start_date,
                    'start_time': self.start_time,
                    'end_time': self.end_time
                })

        elif self.recurrence_type == 'weekly':
            occurrences = self._get_weekly_occurrences(start_date, end_date)

        elif self.recurrence_type == 'daily':
            occurrences = self._get_daily_occurrences(start_date, end_date)

        elif self.recurrence_type == 'monthly':
            occurrences = self._get_monthly_occurrences(start_date, end_date)

        return occurrences

    def _get_weekly_occurrences(self, start_date, end_date):
        """Generate weekly recurring occurrences"""
        occurrences = []

        if not self.recurrence_days:
            return occurrences

        # Parse recurrence days
        target_days = [day.strip().lower() for day in self.recurrence_days.split(',')]
        day_mapping = {
            'monday': 0, 'tuesday': 1, 'wednesday': 2, 'thursday': 3,
            'friday': 4, 'saturday': 5, 'sunday': 6
        }
        target_weekdays = [day_mapping[day] for day in target_days if day in day_mapping]

        # Generate occurrences
        current_date = max(self.start_date, start_date)
        end_limit = min(self.recurrence_end_date or end_date, end_date)

        while current_date <= end_limit:
            if current_date.weekday() in target_weekdays:
                occurrences.append({
                    'date': current_date,
                    'start_time': self.start_time,
                    'end_time': self.end_time
                })

            current_date += timedelta(days=1)

        return occurrences

    def _get_daily_occurrences(self, start_date, end_date):
        """Generate daily recurring occurrences"""
        occurrences = []

        current_date = max(self.start_date, start_date)
        end_limit = min(self.recurrence_end_date or end_date, end_date)

        while current_date <= end_limit:
            occurrences.append({
                'date': current_date,
                'start_time': self.start_time,
                'end_time': self.end_time
            })

            current_date += timedelta(days=self.recurrence_interval)

        return occurrences

    def _get_monthly_occurrences(self, start_date, end_date):
        """Generate monthly recurring occurrences"""
        occurrences = []

        if not self.recurrence_month_day:
            return occurrences

        current_date = max(self.start_date, start_date)
        end_limit = min(self.recurrence_end_date or end_date, end_date)

        # Start from the first occurrence in the range
        year = current_date.year
        month = current_date.month

        while True:
            try:
                occurrence_date = date(year, month, self.recurrence_month_day)
                if occurrence_date > end_limit:
                    break

                if occurrence_date >= current_date:
                    occurrences.append({
                        'date': occurrence_date,
                        'start_time': self.start_time,
                        'end_time': self.end_time
                    })

                # Move to next month
                month += self.recurrence_interval
                if month > 12:
                    year += month // 12
                    month = month % 12
                    if month == 0:
                        month = 12
                        year -= 1

            except ValueError:
                # Invalid date (e.g., Feb 30), skip this month
                month += self.recurrence_interval
                if month > 12:
                    year += month // 12
                    month = month % 12
                    if month == 0:
                        month = 12
                        year -= 1

        return occurrences
```

## Authentication Examples

### 1. Custom Authentication Backend

```python
class CustomAuthentication(JWTAuthentication):
    """Custom authentication supporting JWT and shared access tokens"""

    def authenticate(self, request):
        # Skip authentication for public endpoints
        if self._is_public_endpoint(request):
            return None

        # Try shared access token first
        shared_auth = SharedAccessTokenAuthentication()
        result = shared_auth.authenticate(request)
        if result is not None:
            return result

        # Fall back to JWT authentication
        return super().authenticate(request)

    def _is_public_endpoint(self, request):
        """Check if request is for a public endpoint"""
        view = getattr(request, 'parser_context', {}).get('view', None)
        if view:
            view_class_name = view.__class__.__name__
            action = getattr(view, 'action', None)

            public_endpoints = [
                ('DoctorAvailabilityViewSet', 'available_slots'),
                ('AppointmentViewSet', 'create'),
            ]

            return (view_class_name, action) in public_endpoints

        return False

class SharedAccessTokenAuthentication(BaseAuthentication):
    """Authentication using shared access tokens"""

    def authenticate(self, request):
        # Check for token in query parameters
        token = request.GET.get('shared_token')

        # If no token in query params, check Authorization header
        if not token:
            auth_header = request.headers.get('Authorization', '')
            if auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]

        if not token:
            return None

        try:
            access_token = AccessToken.objects.get(
                token=token,
                is_active=True
            )

            # Check if token is expired
            if access_token.is_expired:
                raise AuthenticationFailed('Access token has expired')

            # Return user and token
            return (access_token.granted_to, access_token)

        except AccessToken.DoesNotExist:
            return None
```

### 2. Permission Classes

```python
class IsOwnerOrReadOnly(BasePermission):
    """Custom permission to only allow owners to edit objects"""

    def has_object_permission(self, request, view, obj):
        # Read permissions for any request
        if request.method in permissions.SAFE_METHODS:
            return True

        # Write permissions only to the owner
        return obj.creator == request.user

class IsAppointmentParticipant(BasePermission):
    """Permission for appointment participants (doctor, patient, creator)"""

    def has_object_permission(self, request, view, obj):
        user = request.user

        # Check if user is involved in the appointment
        if user == obj.doctor or user == obj.patient or user == obj.creator:
            return True

        # Admin users have full access
        if user.role and user.role.name in ['Admin', 'Enterprise Admin']:
            return True

        return False

class IsDoctorOrReadOnly(BasePermission):
    """Permission for doctor-specific resources"""

    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True

        return (
            request.user.is_authenticated and
            request.user.role and
            request.user.role.name == 'doctor'
        )
```

## Validation Examples

### 1. Cross-Field Validation

```python
class AppointmentCreateSerializer(serializers.ModelSerializer):
    def validate(self, data):
        """Cross-field validation for appointment creation"""

        # Validate time consistency
        start_time = data.get('start_time')
        end_time = data.get('end_time')

        if start_time and end_time:
            if start_time >= end_time:
                raise serializers.ValidationError({
                    'end_time': 'End time must be after start time'
                })

            # Validate appointment duration (e.g., max 4 hours)
            duration = end_time - start_time
            if duration.total_seconds() > 4 * 3600:  # 4 hours
                raise serializers.ValidationError({
                    'end_time': 'Appointment duration cannot exceed 4 hours'
                })

        # Validate appointment type consistency
        appointment_type = data.get('appointment_type')
        doctor = data.get('doctor')

        if appointment_type == 'booking' and not doctor:
            raise serializers.ValidationError({
                'doctor': 'Doctor is required for booking appointments'
            })

        if appointment_type == 'manual' and doctor:
            raise serializers.ValidationError({
                'doctor': 'Doctor should not be specified for manual appointments'
            })

        return data

    def validate_doctor_id(self, value):
        """Validate doctor identifier and resolve to user"""
        if not value:
            return value

        try:
            doctor = get_user_by_identifier_or_404(value)

            # Validate doctor role
            if not doctor.role or doctor.role.name != 'doctor':
                raise serializers.ValidationError("User is not a doctor")

            # Check if doctor is active
            if not doctor.is_active:
                raise serializers.ValidationError("Doctor account is inactive")

            return doctor.id  # Return UUID for internal use

        except CustomUser.DoesNotExist:
            raise serializers.ValidationError("Invalid doctor identifier")

    def validate_start_time(self, value):
        """Validate appointment start time"""
        if value <= timezone.now():
            raise serializers.ValidationError(
                "Appointment start time must be in the future"
            )

        # Validate business hours (example: 8 AM to 6 PM)
        if value.hour < 8 or value.hour >= 18:
            raise serializers.ValidationError(
                "Appointments must be scheduled between 8 AM and 6 PM"
            )

        return value
```

### 2. Custom Validation Methods

```python
def validate_appointment_availability(doctor, start_time, end_time, appointment_id=None):
    """Standalone function to validate appointment availability"""

    # Check for conflicts with existing appointments
    conflicts = Appointment.objects.filter(
        doctor=doctor,
        start_time__lt=end_time,
        end_time__gt=start_time,
        status__in=['pending', 'confirmed']
    )

    if appointment_id:
        conflicts = conflicts.exclude(pk=appointment_id)

    if conflicts.exists():
        raise ValidationError(
            f"Time slot conflicts with existing appointment: {conflicts.first().title}"
        )

    # Check if doctor has availability for this time slot
    date = start_time.date()
    time_start = start_time.time()
    time_end = end_time.time()

    # Check overrides first
    override = DoctorAvailabilityOverride.objects.filter(
        doctor=doctor,
        date=date,
        is_active=True
    ).first()

    if override:
        # Check if time falls within override slots
        override_slots = override.time_slots.filter(
            start_time__lte=time_start,
            end_time__gte=time_end
        )
        if not override_slots.exists():
            raise ValidationError("No availability found for this time slot")
        return True

    # Check regular availability
    availabilities = DoctorAvailability.objects.filter(
        doctor=doctor,
        is_active=True
    )

    for availability in availabilities:
        occurrences = availability.get_occurrences(date, date)
        for occurrence in occurrences:
            if (occurrence['start_time'] <= time_start and
                occurrence['end_time'] >= time_end):
                return True

    raise ValidationError("No availability found for this time slot")

def validate_payment_requirements(appointment):
    """Validate payment requirements for appointment"""
    availability = appointment.get_related_availability()

    if availability and availability.need_payment:
        if not appointment.direct_payment and not appointment.insurance:
            raise ValidationError(
                "Payment method required for this appointment. "
                "Please provide payment information or insurance details."
            )

    return True
```

This comprehensive documentation provides practical examples of how Django REST Framework is implemented in the appointments app, covering serializers, viewsets, models, authentication, and validation patterns. The examples demonstrate real-world usage patterns and best practices for building robust APIs.
```
