# Appointments API Quick Reference

This document provides a quick reference for all API endpoints in the appointments app.

## Base URL
All endpoints are prefixed with `/api/appointments/`

## Authentication
- **JWT Token**: Include `Authorization: Bearer <token>` header
- **Public Endpoints**: Some endpoints allow unauthenticated access
- **Shared Access Token**: Use `shared_token` query parameter or Bearer token

## Appointments

### List Appointments
```http
GET /api/appointments/
Authorization: Bearer <token>
```

**Query Parameters:**
- `role`: `doctor` | `patient` - Filter by user role
- `status`: `pending` | `confirmed` | `canceled` | `completed`
- `start_date`: `YYYY-MM-DD` - Filter from date
- `end_date`: `YYYY-MM-DD` - Filter to date
- `doctor`: Doctor custom_url_username or ID
- `patient`: Patient custom_url_username or ID

**Example:**
```http
GET /api/appointments/?role=doctor&status=pending&start_date=2024-01-01
```

### Create Appointment (Public)
```http
POST /api/appointments/
Content-Type: application/json
```

**Request Body:**
```json
{
    "doctor_id": "doctor_username123",
    "start_time": "2024-01-15T10:00:00Z",
    "end_time": "2024-01-15T10:30:00Z",
    "appointment_type": "booking",
    "mode": "video_call",
    "title": "Consultation",
    "email": "<EMAIL>",
    "file_ids": ["file1", "file2"]
}
```

**Response:**
```json
{
    "id": "appointment-uuid",
    "doctor_id": "doctor_username123",
    "patient_id": "patient_username456",
    "start_time": "2024-01-15T10:00:00Z",
    "end_time": "2024-01-15T10:30:00Z",
    "status": "pending",
    "mode": "video_call",
    "title": "Consultation",
    "appointment_type": "booking",
    "attachments": [],
    "created_at": "2024-01-10T12:00:00Z",
    "updated_at": "2024-01-10T12:00:00Z"
}
```

### Get Appointment
```http
GET /api/appointments/{id}/
Authorization: Bearer <token>
```

### Update Appointment
```http
PATCH /api/appointments/{id}/
Authorization: Bearer <token>
Content-Type: application/json
```

**Request Body (Doctor):**
```json
{
    "status": "confirmed",
    "notes": "Please bring your medical records",
    "meeting_link": "https://meet.google.com/abc-def-ghi"
}
```

**Request Body (Patient - Cancel):**
```json
{
    "status": "canceled",
    "cancellation_reason": "Schedule conflict"
}
```

### Delete Appointment
```http
DELETE /api/appointments/{id}/
Authorization: Bearer <token>
```

## Doctor Availability

### List Availabilities
```http
GET /api/appointments/availabilities/
Authorization: Bearer <token>
```

### Create Availability
```http
POST /api/appointments/availabilities/
Authorization: Bearer <token>
Content-Type: application/json
```

**Request Body (Single Date):**
```json
{
    "title": "Morning Consultations",
    "start_date": "2024-01-15",
    "start_time": "09:00:00",
    "end_time": "12:00:00",
    "recurrence_type": "none",
    "mode": "video_call,in_person",
    "need_payment": true
}
```

**Request Body (Weekly Recurring):**
```json
{
    "title": "Weekly Consultations",
    "start_date": "2024-01-01",
    "start_time": "09:00:00",
    "end_time": "17:00:00",
    "recurrence_type": "weekly",
    "recurrence_days": "monday,wednesday,friday",
    "recurrence_end_date": "2024-12-31",
    "mode": "video_call,in_person",
    "need_payment": true
}
```

### Get Available Slots (Public)
```http
GET /api/appointments/availabilities/available_slots/
```

**Query Parameters:**
- `doctor`: Doctor custom_url_username (required)
- `start_date`: `YYYY-MM-DD` (required)
- `end_date`: `YYYY-MM-DD` (required)
- `mode`: Filter by appointment mode

**Example:**
```http
GET /api/appointments/availabilities/available_slots/?doctor=dr_smith123&start_date=2024-01-15&end_date=2024-01-22
```

**Response:**
```json
{
    "doctor": "dr_smith123",
    "available_slots": [
        {
            "date": "2024-01-15",
            "slots": [
                {
                    "start_time": "09:00:00",
                    "end_time": "09:30:00",
                    "mode": ["video_call", "in_person"],
                    "need_payment": true
                },
                {
                    "start_time": "10:00:00",
                    "end_time": "10:30:00",
                    "mode": ["video_call", "in_person"],
                    "need_payment": true
                }
            ]
        }
    ]
}
```

### Get Availability by Doctor (Public)
```http
GET /api/appointments/availabilities/by_doctor/
```

**Query Parameters:**
- `doctor`: Doctor custom_url_username (required)
- `start_date`: `YYYY-MM-DD` (optional)
- `end_date`: `YYYY-MM-DD` (optional)

**Example:**
```http
GET /api/appointments/availabilities/by_doctor/?doctor=dr_smith123&start_date=2024-01-01
```

### Update Availability
```http
PATCH /api/appointments/availabilities/{id}/
Authorization: Bearer <token>
Content-Type: application/json
```

**Request Body:**
```json
{
    "need_payment": false,
    "mode": "video_call"
}
```

### Delete Availability
```http
DELETE /api/appointments/availabilities/{id}/
Authorization: Bearer <token>
```

## Doctor Consultation Profiles

### List Consultation Profiles
```http
GET /api/appointments/doctor-consultation-profiles/
Authorization: Bearer <token>
```

### Create Consultation Profile
```http
POST /api/appointments/doctor-consultation-profiles/
Authorization: Bearer <token>
Content-Type: application/json
```

**Request Body:**
```json
{
    "consultation_fee": 5000,
    "consultation_duration": 30,
    "accepts_telemedicine": true,
    "bio": "Experienced family physician specializing in preventive care",
    "specializations": ["Family Medicine", "Preventive Care"],
    "languages": ["English", "Spanish"]
}
```

### Get Consultation Profile
```http
GET /api/appointments/doctor-consultation-profiles/{id}/
Authorization: Bearer <token>
```

### Update Consultation Profile
```http
PATCH /api/appointments/doctor-consultation-profiles/{id}/
Authorization: Bearer <token>
Content-Type: application/json
```

**Request Body:**
```json
{
    "consultation_fee": 6000,
    "bio": "Updated bio with new specializations"
}
```

## Availability Overrides

### List Overrides
```http
GET /api/appointments/overrides/
Authorization: Bearer <token>
```

### Create Override
```http
POST /api/appointments/overrides/
Authorization: Bearer <token>
Content-Type: application/json
```

**Request Body:**
```json
{
    "date": "2024-01-15",
    "time_slots": [
        {
            "start_time": "09:00:00",
            "end_time": "12:00:00"
        },
        {
            "start_time": "14:00:00",
            "end_time": "17:00:00"
        }
    ]
}
```

## Doctor Messages & Contact Info

### List Doctor Messages
```http
GET /api/appointments/doctor-messages/
Authorization: Bearer <token>
```

### Create Doctor Message
```http
POST /api/appointments/doctor-messages/
Authorization: Bearer <token>
Content-Type: application/json
```

### Get Doctor Contact Info
```http
GET /api/appointments/doctor-contact-info/
Authorization: Bearer <token>
```

### Update Doctor Contact Info
```http
PATCH /api/appointments/doctor-contact-info/{id}/
Authorization: Bearer <token>
Content-Type: application/json
```

## Google Calendar Integration

### Connect Google Account
```http
GET /api/appointments/google-auth/
Authorization: Bearer <token>
```

### Google Auth Callback
```http
GET /api/appointments/google-auth/callback/?code=<auth_code>
Authorization: Bearer <token>
```

### Disconnect Google Account
```http
POST /api/appointments/google-auth/disconnect/
Authorization: Bearer <token>
```

## Error Responses

### Validation Error
```json
{
    "error": "Validation failed",
    "detail": "Invalid input data",
    "field_errors": {
        "start_time": ["This field is required"],
        "doctor_id": ["Invalid doctor identifier"]
    }
}
```

### Permission Error
```json
{
    "error": "Permission denied",
    "detail": "You do not have permission to perform this action"
}
```

### Not Found Error
```json
{
    "error": "Not found",
    "detail": "Appointment not found"
}
```

### Conflict Error
```json
{
    "error": "Scheduling conflict",
    "detail": "Time slot conflicts with existing appointment"
}
```

## Status Codes

- `200 OK`: Successful GET, PATCH requests
- `201 Created`: Successful POST requests
- `204 No Content`: Successful DELETE requests
- `400 Bad Request`: Validation errors, invalid input
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Permission denied
- `404 Not Found`: Resource not found
- `409 Conflict`: Scheduling conflicts, business rule violations

## Rate Limiting

- **Authenticated requests**: 1000 requests per hour
- **Public endpoints**: 100 requests per hour per IP
- **Appointment creation**: 10 requests per hour per IP

## Custom Headers

### Request Headers
- `Authorization`: Bearer token for authentication
- `Content-Type`: application/json for POST/PATCH requests
- `X-Access-Token`: Shared access token (alternative to Authorization)

### Response Headers
- `X-RateLimit-Remaining`: Remaining requests in current window
- `X-RateLimit-Reset`: Time when rate limit resets
- `X-Request-ID`: Unique request identifier for debugging
