# Django REST Framework Architecture Guide - Appointments App

## Table of Contents
1. [Basic Overview](#basic-overview)
2. [Models Architecture](#models-architecture)
3. [Serializers](#serializers)
4. [ViewSets and Views](#viewsets-and-views)
5. [API Endpoints](#api-endpoints)
6. [Authentication and Permissions](#authentication-and-permissions)
7. [CRUD Operations](#crud-operations)
8. [Custom Features](#custom-features)
9. [Request/Response Flow](#requestresponse-flow)
10. [Business Logic](#business-logic)

## Basic Overview

The appointments app implements a comprehensive Django REST Framework (DRF) architecture for managing medical appointments, doctor availability, and consultation profiles. The app follows DRF best practices with:

- **ModelViewSets** for full CRUD operations
- **Custom serializers** for different operations (create, update, display)
- **Role-based permissions** with custom authentication
- **Custom URL username masking** for public APIs
- **Flexible availability system** with recurrence patterns
- **Telemedicine integration** with payment processing

### Core Components

```
appointments/
├── models/           # Django models
├── api/
│   ├── serializers/  # DRF serializers
│   └── views/        # DRF viewsets and views
├── services/         # Business logic services
└── tasks.py          # Celery background tasks
```

## Models Architecture

### Core Models Hierarchy

The appointments app uses a sophisticated model structure with the following relationships:

- **CustomUser** → **Appointment** (creator, patient, doctor relationships)
- **CustomUser** → **DoctorAvailability** (doctor availability management)
- **CustomUser** → **DoctorConsultationProfile** (telemedicine profiles)
- **Appointment** → **AppointmentAttachment** (file attachments)
- **DoctorAvailabilityOverride** → **DoctorAvailabilityOverrideSlot** (custom time slots)

### 1. Appointment Model

The central model for storing appointment information with support for both manual events and doctor bookings.

**Key Fields:**
- `creator`: User who created the appointment
- `patient`: Patient (same as creator for manual, different for booking)
- `doctor`: Doctor (null for manual events)
- `appointment_type`: 'manual' or 'booking'
- `status`: 'pending', 'confirmed', 'canceled', 'completed'
- `mode`: 'in_person', 'video_call', 'phone_call'
- `start_time`/`end_time`: Appointment timing
- `slot_reference`: Reference to availability slot

**Business Logic:**
- Validates availability conflicts for booking appointments
- Supports both clinic and enterprise contexts
- Handles Google Calendar integration
- Manages payment requirements

### 2. DoctorAvailability Model

Flexible availability system supporting recurring patterns.

**Key Fields:**
- `doctor`: Doctor user
- `recurrence_type`: 'none', 'daily', 'weekly', 'monthly', 'yearly'
- `recurrence_interval`: Interval for recurrence
- `recurrence_days`: Days for weekly recurrence
- `need_payment`: Whether slots require payment
- `mode`: Supported appointment modes

**Recurrence Support:**
- Non-recurring: Single date availability
- Daily: Every N days
- Weekly: Specific days of week
- Monthly: Specific day of month
- Yearly: Annual recurrence

### 3. DoctorConsultationProfile Model

Telemedicine pricing and profile information.

**Key Fields:**
- `consultation_fee`: Fee in cents
- `consultation_duration`: Duration in minutes
- `accepts_telemedicine`: Boolean flag
- `specializations`: JSON array
- `languages`: JSON array
- `stripe_account_setup`: Payment setup status

## Serializers

### Serializer Strategy

The app uses different serializers for different operations to optimize data handling:

1. **Create Serializers**: Handle input validation and creation logic
2. **Update Serializers**: Manage updates with appropriate field restrictions
3. **Display Serializers**: Optimize output with computed fields and masking

### 1. AppointmentCreateSerializer

**Purpose**: Handle appointment creation with complex validation

**Key Features:**
- Supports both authenticated and unauthenticated users
- Auto-creates users for unauthenticated requests
- Validates availability conflicts
- Handles file attachments
- Manages payment requirements

### 2. AppointmentSerializer (Display)

**Purpose**: Optimized for API responses with custom URL masking

**Key Features:**
- Returns `custom_url_username` instead of internal IDs
- Different behavior for booking vs manual appointments
- Read-only computed fields
- Attachment information

### 3. DoctorAvailabilitySerializer

**Purpose**: Manage doctor availability with recurrence patterns

**Key Features:**
- Validates recurrence patterns
- Handles time zone considerations
- Supports mode restrictions
- Payment requirement configuration

## ViewSets and Views

### 1. AppointmentViewSet (ModelViewSet)

**Base Class**: `viewsets.ModelViewSet`
**URL Pattern**: `/api/appointments/`

**Dynamic Serializer Selection:**
```python
def get_serializer_class(self):
    if self.action in ['create']:
        return AppointmentCreateSerializer
    elif self.action in ['update', 'partial_update']:
        return AppointmentUpdateSerializer
    elif self.action in ['list', 'retrieve']:
        return AppointmentSerializer
    return AppointmentCreateSerializer
```

**Dynamic Permissions:**
```python
def get_permissions(self):
    if self.action == 'create':
        return [AllowAny()]  # Public appointment creation
    return [IsAuthenticated()]
```

**Custom Queryset Filtering:**
- Role-based filtering (doctor/patient view)
- Date range filtering
- Status filtering
- Custom URL username support

### 2. DoctorAvailabilityViewSet (ModelViewSet)

**Base Class**: `viewsets.ModelViewSet`
**URL Pattern**: `/api/appointments/availabilities/`

**Custom Actions:**
- `available_slots`: Public endpoint for slot booking
- `by_doctor`: Get availability by custom URL username

**Public Access:**
```python
@action(detail=False, methods=['get'], permission_classes=[AllowAny])
def available_slots(self, request):
    # Public endpoint for appointment booking
```

### 3. DoctorConsultationProfileViewSet (ModelViewSet)

**Base Class**: `viewsets.ModelViewSet`
**URL Pattern**: `/api/appointments/doctor-consultation-profiles/`

**Features:**
- Doctor-specific profile management
- Telemedicine configuration
- Payment setup validation

## API Endpoints

### Complete Endpoint List

| Endpoint | Method | Description | Auth Required |
|----------|--------|-------------|---------------|
| `/api/appointments/` | GET | List appointments | ✓ |
| `/api/appointments/` | POST | Create appointment | ✗ (Public) |
| `/api/appointments/{id}/` | GET | Get appointment | ✓ |
| `/api/appointments/{id}/` | PUT/PATCH | Update appointment | ✓ |
| `/api/appointments/{id}/` | DELETE | Delete appointment | ✓ |
| `/api/appointments/availabilities/` | GET | List availabilities | ✓ |
| `/api/appointments/availabilities/available_slots/` | GET | Get available slots | ✗ (Public) |
| `/api/appointments/availabilities/by_doctor/` | GET | Get by doctor | ✗ (Public) |
| `/api/appointments/doctor-consultation-profiles/` | GET | List profiles | ✓ |
| `/api/appointments/overrides/` | GET | List overrides | ✓ |

### Query Parameters

**Appointments:**
- `role`: Filter by user role (doctor/patient)
- `status`: Filter by appointment status
- `start_date`/`end_date`: Date range filtering
- `doctor`: Doctor custom_url_username or ID
- `patient`: Patient custom_url_username or ID

**Available Slots:**
- `doctor`: Doctor custom_url_username (required)
- `start_date`/`end_date`: Date range
- `mode`: Appointment mode filter

## Authentication and Permissions

### Authentication System

The app uses a multi-layered authentication system:

1. **JWT Authentication**: Primary authentication method
2. **Shared Access Token**: For special access scenarios
3. **Public Endpoints**: Specific endpoints allow unauthenticated access

### Permission Classes

**Built-in Permissions:**
- `IsAuthenticated`: Requires valid authentication
- `AllowAny`: Public access

**Custom Permission Logic:**
```python
def get_permissions(self):
    if self.action == 'create':
        return [AllowAny()]  # Public appointment creation
    return [IsAuthenticated()]
```

### Role-Based Access Control

**User Roles:**
- `Doctor`: Can manage their availability and appointments
- `Patient`: Can book appointments and view their appointments
- `Admin`: Full system access
- `Enterprise Admin`: Enterprise-scoped access

**Permission Validation:**
```python
def update(self, request, pk=None):
    appointment = self.get_object()
    is_doctor = appointment.doctor == request.user
    is_patient = appointment.patient == request.user
    is_creator = appointment.creator == request.user
    
    # Role-specific update logic
```

### Public Endpoints

Certain endpoints are public to support appointment booking:

1. **Appointment Creation** (`POST /api/appointments/`)
   - Allows unauthenticated users to book appointments
   - Auto-creates user accounts from email

2. **Available Slots** (`GET /api/appointments/availabilities/available_slots/`)
   - Public access to doctor availability
   - Uses custom URL usernames instead of IDs

3. **Doctor Lookup** (`GET /api/appointments/availabilities/by_doctor/`)
   - Public doctor availability lookup
   - Supports custom URL username resolution

## CRUD Operations

### Create Operations

#### 1. Appointment Creation

**Endpoint**: `POST /api/appointments/`
**Serializer**: `AppointmentCreateSerializer`
**Authentication**: Public (AllowAny)

**Process Flow:**
1. Validate input data
2. Handle user authentication/creation
3. Validate doctor availability
4. Check payment requirements
5. Create appointment
6. Process file attachments
7. Trigger background tasks (calendar sync, notifications)

**Example Request:**
```json
{
    "doctor_id": "doctor123",
    "start_time": "2024-01-15T10:00:00Z",
    "end_time": "2024-01-15T10:30:00Z",
    "appointment_type": "booking",
    "mode": "video_call",
    "title": "Consultation",
    "email": "<EMAIL>",
    "file_ids": ["file1", "file2"]
}
```

#### 2. Availability Creation

**Endpoint**: `POST /api/appointments/availabilities/`
**Serializer**: `DoctorAvailabilitySerializer`
**Authentication**: Required

**Recurrence Example:**
```json
{
    "title": "Weekly Consultations",
    "start_date": "2024-01-01",
    "start_time": "09:00:00",
    "end_time": "17:00:00",
    "recurrence_type": "weekly",
    "recurrence_days": "monday,wednesday,friday",
    "recurrence_end_date": "2024-12-31",
    "mode": "video_call,in_person",
    "need_payment": true
}
```

### Read Operations

#### 1. Appointment Listing

**Endpoint**: `GET /api/appointments/`
**Serializer**: `AppointmentSerializer`
**Authentication**: Required

**Filtering Examples:**
```
GET /api/appointments/?role=doctor&status=pending
GET /api/appointments/?start_date=2024-01-01&end_date=2024-01-31
GET /api/appointments/?doctor=doctor_username123
```

**Response Features:**
- Custom URL username masking
- Role-based filtering
- Computed fields (meeting links, payment status)

#### 2. Available Slots (Public)

**Endpoint**: `GET /api/appointments/availabilities/available_slots/`
**Authentication**: Public

**Example:**
```
GET /api/appointments/availabilities/available_slots/?doctor=doctor_username123&start_date=2024-01-15&end_date=2024-01-22
```

**Response:**
```json
{
    "available_slots": [
        {
            "date": "2024-01-15",
            "slots": [
                {
                    "start_time": "09:00:00",
                    "end_time": "09:30:00",
                    "mode": ["video_call", "in_person"],
                    "need_payment": true
                }
            ]
        }
    ]
}
```

### Update Operations

#### 1. Appointment Updates

**Endpoint**: `PUT/PATCH /api/appointments/{id}/`
**Serializer**: `AppointmentUpdateSerializer`
**Authentication**: Required

**Role-Based Update Logic:**
- **Doctors**: Can update status, notes, meeting links
- **Patients**: Can cancel appointments with reason
- **Creators**: Full control over manual appointments

**Restricted Fields:**
```python
read_only_fields = (
    'creator', 'patient', 'doctor',
    'appointment_type', 'slot_reference',
    'start_time', 'end_time',
    'cancelled_by'
)
```

#### 2. Availability Updates

**Endpoint**: `PUT/PATCH /api/appointments/availabilities/{id}/`
**Serializer**: `DoctorAvailabilitySerializer`
**Authentication**: Required

**Validation:**
- Recurrence pattern consistency
- Time zone handling
- Conflict detection with existing appointments

### Delete Operations

#### 1. Appointment Deletion

**Endpoint**: `DELETE /api/appointments/{id}/`
**Authentication**: Required

**Business Logic:**
- Soft delete for booking appointments
- Hard delete for manual events
- Notification triggers
- Calendar cleanup

#### 2. Availability Deletion

**Endpoint**: `DELETE /api/appointments/availabilities/{id}/`
**Authentication**: Required

**Cascade Effects:**
- Affects future appointment slots
- Triggers rescheduling notifications
- Updates calendar integrations

## Custom Features

### 1. Custom URL Username Masking

**Purpose**: Hide internal user IDs from public APIs

**Implementation:**
```python
def get_doctor_id(self, obj):
    """Return custom_url_username for booking appointments, UUID for manual appointments"""
    if obj.appointment_type == 'booking' and obj.doctor:
        return obj.doctor.custom_url_username
    elif obj.doctor:
        return str(obj.doctor.id)
    return None
```

**Benefits:**
- Enhanced privacy
- SEO-friendly URLs
- Consistent public API interface

### 2. Dual Identifier Support

**Purpose**: Accept both custom usernames and UUIDs

**Helper Function:**
```python
from accounts.helpers import get_user_by_identifier_or_404

doctor = get_user_by_identifier_or_404(doctor_identifier)
```

**Usage:**
- Public APIs use custom usernames
- Internal APIs support both formats
- Backward compatibility maintained

### 3. Payment Integration

**Purpose**: Integrate telemedicine payments

**Features:**
- `need_payment` field on availability
- Payment validation during booking
- Stripe integration
- P2P payment flow

**Implementation:**
```python
def validate_payment_requirements(self, appointment):
    availability = appointment.get_related_availability()
    if availability and availability.need_payment:
        # Validate payment method or initiate payment flow
        pass
```

### 4. Recurrence Pattern System

**Purpose**: Flexible recurring availability

**Supported Patterns:**
- **None**: Single occurrence
- **Daily**: Every N days
- **Weekly**: Specific weekdays
- **Monthly**: Specific day of month
- **Yearly**: Annual recurrence

**Implementation:**
```python
def get_occurrences(self, start_date, end_date):
    """Generate occurrence dates based on recurrence pattern"""
    if self.recurrence_type == 'weekly':
        return self._get_weekly_occurrences(start_date, end_date)
    # ... other patterns
```

### 5. File Attachment System

**Purpose**: Support document uploads

**Features:**
- Multiple file attachments per appointment
- File validation and processing
- Secure file storage
- Attachment metadata

**Usage:**
```python
# In serializer
file_ids = serializers.ListField(
    child=serializers.CharField(),
    write_only=True,
    required=False
)

def create(self, validated_data):
    file_ids = validated_data.pop('file_ids', [])
    appointment = super().create(validated_data)

    # Process file attachments
    for file_id in file_ids:
        # Create AppointmentAttachment

    return appointment
```

### 6. Google Calendar Integration

**Purpose**: Sync appointments with Google Calendar

**Features:**
- Automatic event creation
- Two-way synchronization
- Meeting link generation
- Reminder management

**Background Tasks:**
```python
# In tasks.py
@shared_task
def sync_appointment_to_google_calendar(appointment_id):
    # Sync appointment to Google Calendar
    pass

@shared_task
def send_appointment_reminder(appointment_id):
    # Send appointment reminders
    pass
```

## Request/Response Flow

### 1. Public Appointment Creation Flow

```
Client → API → Serializer → Models → Tasks
  ↓       ↓        ↓         ↓       ↓
POST    Validate  Create    Save    Background
/api/   Input     User      Data    Processing
appts/  Data      Account
```

**Detailed Steps:**
1. Client sends POST request with appointment data
2. API routes to AppointmentViewSet.create()
3. AppointmentCreateSerializer validates input
4. Serializer creates/gets user account if unauthenticated
5. Availability validation checks doctor schedule
6. Appointment model is created with validated data
7. File attachments are processed and linked
8. Background tasks are triggered for calendar sync and notifications

### 2. Available Slots Query Flow

```
Client → API → ViewSet → Helper → Models → Response
  ↓       ↓       ↓        ↓       ↓        ↓
GET     Route   Resolve  Custom  Query    Available
/slots  Public  Doctor   URL     Avail.   Slots
```

**Detailed Steps:**
1. Client queries available slots with doctor username
2. API routes to DoctorAvailabilityViewSet.available_slots()
3. Helper function resolves custom_url_username to user
4. ViewSet queries doctor's availability records
5. Models calculate occurrence dates based on recurrence patterns
6. System checks for appointment conflicts
7. Response returns available time slots

### 3. Authenticated Update Flow

```
Client → Auth → ViewSet → Permissions → Serializer → Models
  ↓       ↓       ↓          ↓           ↓           ↓
PATCH   JWT     Check      Role-based  Validate    Update
/appt/  Token   Auth       Access      Changes     Database
123/
```

**Detailed Steps:**
1. Client sends PATCH request with JWT token
2. Authentication middleware validates token
3. ViewSet receives authenticated request
4. Permission checking based on user role and appointment ownership
5. AppointmentUpdateSerializer validates update data
6. Model validation ensures business rules
7. Database is updated with changes

## Business Logic

### 1. Appointment Validation

**Availability Conflict Detection:**
```python
def clean(self):
    if self.appointment_type == 'booking':
        # Check for existing appointments in the same time slot
        conflicts = Appointment.objects.filter(
            doctor=self.doctor,
            start_time__lt=self.end_time,
            end_time__gt=self.start_time,
            status__in=['pending', 'confirmed']
        ).exclude(pk=self.pk)

        if conflicts.exists():
            raise ValidationError("Time slot conflicts with existing appointment")
```

**Payment Requirement Validation:**
```python
def validate_payment_requirements(self):
    availability = self.get_related_availability()
    if availability and availability.need_payment:
        # Check if payment method is provided or payment is completed
        if not self.has_valid_payment():
            raise ValidationError("Payment required for this appointment")
```

### 2. Availability Calculation

**Occurrence Generation:**
```python
def get_occurrences(self, start_date, end_date):
    occurrences = []

    if self.recurrence_type == 'weekly':
        current_date = self.start_date
        while current_date <= end_date:
            if current_date.strftime('%A').lower() in self.recurrence_days.split(','):
                if start_date <= current_date <= end_date:
                    occurrences.append({
                        'date': current_date,
                        'start_time': self.start_time,
                        'end_time': self.end_time
                    })
            current_date += timedelta(days=1)

    return occurrences
```

### 3. Role-Based Access Control

**Permission Checking:**
```python
def has_appointment_access(self, user, appointment):
    """Check if user has access to appointment"""
    if user == appointment.doctor:
        return True
    if user == appointment.patient:
        return True
    if user == appointment.creator:
        return True
    if user.role and user.role.name in ['Admin', 'Enterprise Admin']:
        return True
    return False
```

### 4. Custom URL Resolution

**Identifier Resolution:**
```python
def get_user_by_identifier(identifier):
    """Get user by custom_url_username or UUID"""
    try:
        # Try UUID first
        return CustomUser.objects.get(id=identifier)
    except (CustomUser.DoesNotExist, ValueError):
        # Try custom_url_username
        return CustomUser.objects.get(custom_url_username=identifier)
```

## Error Handling

### Common Error Patterns

1. **Validation Errors**: Field-level and object-level validation
2. **Permission Errors**: Role-based access control
3. **Conflict Errors**: Appointment scheduling conflicts
4. **Not Found Errors**: Invalid identifiers or missing resources

### Error Response Format

```json
{
    "error": "Validation failed",
    "detail": "Specific error message",
    "field_errors": {
        "start_time": ["This field is required"],
        "doctor_id": ["Invalid doctor identifier"]
    }
}
```

### Custom Exception Handling

```python
# In serializers
def validate_doctor_id(self, value):
    try:
        doctor = get_user_by_identifier_or_404(value)
        if not doctor.role or doctor.role.name != 'doctor':
            raise serializers.ValidationError("User is not a doctor")
        return value
    except CustomUser.DoesNotExist:
        raise serializers.ValidationError("Invalid doctor identifier")
```

## Performance Considerations

### 1. Query Optimization

**Select Related Usage:**
```python
def get_queryset(self):
    return Appointment.objects.select_related(
        'doctor', 'patient', 'creator', 'clinic'
    ).prefetch_related('attachments__file')
```

**Custom Querysets:**
- Role-based filtering at database level
- Date range optimization with indexes
- Pagination for large result sets

### 2. Caching Strategy

**User Permissions Caching:**
```python
# Cache user permissions for 5 minutes
cache_key = f'user_permissions_{request.user.id}'
user_permissions = cache.get(cache_key)
if user_permissions is None:
    user_permissions = set(request.user.role.permissions.values_list('codename', flat=True))
    cache.set(cache_key, user_permissions, 300)
```

**Availability Calculations:**
- Cache recurring pattern calculations
- Cache custom URL mappings
- Use Redis for session-based caching

### 3. Background Processing

**Asynchronous Tasks:**
- Calendar synchronization
- Email/SMS notifications
- Payment processing
- File processing

**Task Examples:**
```python
@shared_task
def process_appointment_creation(appointment_id):
    appointment = Appointment.objects.get(id=appointment_id)

    # Sync to Google Calendar
    sync_appointment_to_google_calendar.delay(appointment_id)

    # Send notifications
    send_appointment_confirmation.delay(appointment_id)

    # Process payments if required
    if appointment.requires_payment():
        process_appointment_payment.delay(appointment_id)
```

## Testing Strategy

### 1. Unit Tests

**Model Tests:**
```python
class AppointmentModelTest(TestCase):
    def test_appointment_validation(self):
        # Test availability conflict detection
        # Test payment requirement validation
        # Test recurrence pattern calculation
```

**Serializer Tests:**
```python
class AppointmentSerializerTest(TestCase):
    def test_create_serializer_validation(self):
        # Test field validation
        # Test business logic validation
        # Test custom URL username handling
```

### 2. Integration Tests

**API Endpoint Tests:**
```python
class AppointmentAPITest(APITestCase):
    def test_public_appointment_creation(self):
        # Test unauthenticated appointment creation
        # Test user account auto-creation
        # Test availability validation

    def test_authenticated_appointment_management(self):
        # Test role-based permissions
        # Test CRUD operations
        # Test filtering and pagination
```

### 3. End-to-End Tests

**Complete Flow Tests:**
- Appointment booking flow from public API
- Payment integration testing
- Calendar synchronization testing
- Notification delivery testing

## Security Considerations

### 1. Data Privacy

**Custom URL Masking:**
- Internal UUIDs never exposed in public APIs
- Custom usernames provide privacy layer
- Consistent masking across all public endpoints

### 2. Access Control

**Permission Layers:**
- Authentication at middleware level
- Authorization at view level
- Object-level permissions for sensitive operations

### 3. Input Validation

**Comprehensive Validation:**
- Field-level validation in serializers
- Model-level validation for business rules
- Cross-field validation for complex scenarios

## Deployment Considerations

### 1. Environment Configuration

**Settings Management:**
- Separate settings for development/staging/production
- Environment-specific authentication backends
- Configurable caching and task queue settings

### 2. Database Optimization

**Indexing Strategy:**
- Indexes on frequently queried fields
- Composite indexes for complex queries
- Regular maintenance and optimization

### 3. Monitoring and Logging

**Comprehensive Logging:**
- API request/response logging
- Error tracking and alerting
- Performance monitoring
- Business metrics tracking

## Conclusion

The appointments app demonstrates a sophisticated DRF implementation with:

- **Flexible Architecture**: Supports multiple appointment types and patterns
- **Security**: Role-based permissions with custom URL masking
- **Scalability**: Background task processing and caching
- **Integration**: Payment processing and calendar synchronization
- **User Experience**: Public APIs for seamless booking
- **Maintainability**: Clear separation of concerns and comprehensive testing

This architecture provides a solid foundation for medical appointment management while maintaining security, performance, and extensibility. The implementation follows DRF best practices and demonstrates advanced patterns for complex business requirements.
