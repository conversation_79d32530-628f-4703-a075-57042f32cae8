from datetime import timedelta
from rest_framework import viewsets, status, generics, permissions
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.views import APIView
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Parser, JSONParser
from rest_framework.pagination import PageNumberPagination
from django.contrib.auth.models import Permission
from django.shortcuts import get_object_or_404
from django.utils import timezone, translation
from django.db.models import Q
from django.contrib.postgres.search import TrigramSimilarity
from django.db import transaction
from django.http import JsonResponse
from django.conf import settings
from google.cloud import storage
from google.oauth2 import service_account
from google.cloud.exceptions import NotFound
from google.cloud import translate_v2 as translate
import os
import json
import secrets
import logging

# Models
from accounts.models import CustomUser, ProfileCategory, ProfileCategoryContent
from ..models import (
    CustomPermission, Role, ActivityLog, Profile, Community, Education, 
    ResearchPaper, Award, YouTubeVideo, PracticeLocation, CredentialDocument, 
    CustomInformation
)
from roles.models.access_token import AccessToken
from roles.models.shared_access import SharedAccess
from enterprise.models import EnterpriseMember

# Serializers
from accounts.serializer import ProfileCategorySerializer
from ..serializers import (
    BulkRoleAssignmentSerializer, RoleSerializer, UserEmailSerializer, 
    UserListSerializer, UserRoleAssignmentSerializer, RoleCreationSerializer, 
    RoleUpdateSerializer, PermissionSerializer, FirstAdminUserSerializer, 
    UserStatusSerializer, CommunitySerializer, UserProfileSerializer, 
    ResearchPaperSerializer, AwardSerializer, YouTubeVideoSerializer, 
    PracticeLocationSerializer, UserProfileAdminSerializer, EducationSerializer,
    CredentialVerificationSerializer, ActivityLogSerializer, ProfileToggleSerializer,
    ProfileFieldsVisibilitySerializer
)

# Permissions
from roles.permissions import (
    IsAdministrator, IsDoctor, CanCreateCommunity, IsAdminOrModerator
)

# Utils
from config.pagination_utils import paginate_queryset
from roles.send_cred_email import (
    send_credential_verification_email, send_credential_submission_email
)
from roles.activity_utils import log_activity
from roles.gcp_utils import (
    get_enterprise_logo_signed_url, upload_file_to_gcs, get_signed_url, 
    get_private_profile_picture_signed_url, get_background_image_signed_url
)
from roles.compression_helper import compress_image
from roles.translation_utils import TranslationService

logger = logging.getLogger(__name__) 