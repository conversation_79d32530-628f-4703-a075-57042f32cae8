from .mixins import *


class RoleViewSet(viewsets.ModelViewSet):
    queryset = Role.objects.all()
    serializer_class = RoleSerializer
    # permission_classes = [IsAdministrator, IsAuthenticated]

    def get_serializer_class(self):
        if self.action == 'create':
            return RoleCreationSerializer
        elif self.action in ['update', 'partial_update']:
            return RoleUpdateSerializer
        return self.serializer_class

    @action(detail=False, methods=['get'])
    def available_permissions(self, request):
        custom_permissions = CustomPermission.objects.all()
        return Response(PermissionSerializer(custom_permissions, many=True).data)

    @action(detail=False, methods=['post'])
    def assign_role(self, request):
        serializer = UserRoleAssignmentSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']
            role_id = serializer.validated_data['role_id']
            
            try:
                user = CustomUser.objects.get(email=email)
                role = Role.objects.get(id=role_id)
                user.role = role
                user.save()
                log_activity(request.user, "Assigned Role", f"User: {user.email}", f"Assigned role: {role.name}")
                return Response({'message': f'Role {role.name} assigned to user {user.email}'}, status=status.HTTP_200_OK)
            except CustomUser.DoesNotExist:
                return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)
            except Role.DoesNotExist:
                return Response({'error': 'Role not found'}, status=status.HTTP_404_NOT_FOUND)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def create_role(self, request):
        serializer = RoleCreationSerializer(data=request.data)
        if serializer.is_valid():
            role = serializer.save()
            return Response(RoleSerializer(role).data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def check_permissions(self, request):
        user = request.user
        if not user.is_authenticated:
            return Response({"error": "User is not authenticated"}, status=status.HTTP_401_UNAUTHORIZED)
        
        # Check if user has a role
        if not user.role:
            return Response({
                "is_administrator": False,
                "is_doctor": False,
                "is_moderator": False,
                "can_create_community": False,
                "can_update_community": False,
                "can_delete_community": False,
                "can_manage_members": False,
                "has_role": False
            })
        
        permissions = {
            "is_administrator": user.role.name == 'Administrator',
            "is_doctor": user.role.name == 'Doctor',
            "is_moderator": user.role.name == 'Moderator',
            "can_create_community": user.role.name in ['Administrator', 'Doctor'],
            "can_update_community": user.role.name in ['Administrator', 'Moderator'],
            "can_delete_community": user.role.name == 'Administrator',
            "can_manage_members": user.role.name in ['Administrator', 'Moderator'],
            "has_role": True
        }
        
        return Response(permissions)

    @action(detail=False, methods=['get'])
    def list_users(self, request):
        """List all users with pagination and filtering"""
        queryset = CustomUser.objects.all()
        
        # Filter by role
        role_name = request.query_params.get('role', None)
        if role_name:
            queryset = queryset.filter(role__name=role_name)
            
        # Search by email
        search = request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(email__icontains=search)
            
        # Pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = UserListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
            
        serializer = UserListSerializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def bulk_assign_role(self, request):
        """Assign role to multiple users"""
        serializer = BulkRoleAssignmentSerializer(data=request.data)
        if serializer.is_valid():
            role_id = serializer.validated_data['role_id']
            user_emails = serializer.validated_data['user_emails']
            
            try:
                role = Role.objects.get(id=role_id)
                users = CustomUser.objects.filter(email__in=user_emails)
                
                for user in users:
                    user.role = role
                    user.save()
                    log_activity(request.user, "Bulk Assigned Role", f"User: {user.email}", f"Assigned role: {role.name}")
                
                return Response({
                    'message': f'Role {role.name} assigned to {len(users)} users'
                }, status=status.HTTP_200_OK)
            except Role.DoesNotExist:
                return Response({'error': 'Role not found'}, status=status.HTTP_404_NOT_FOUND)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def revoke_role(self, request):
        """Revoke role from a user"""
        serializer = UserRoleAssignmentSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']
            
            try:
                user = CustomUser.objects.get(email=email)
                old_role = user.role.name if user.role else None
                user.role = None
                user.save()
                log_activity(request.user, "Revoked Role", f"User: {user.email}", f"Revoked role: {old_role}")
                return Response({
                    'message': f'Role revoked from user {user.email}'
                }, status=status.HTTP_200_OK)
            except CustomUser.DoesNotExist:
                return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def toggle_user_status(self, request):
        """Enable/disable a user"""
        serializer = UserStatusSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']
            is_active = serializer.validated_data['is_active']
            
            try:
                user = CustomUser.objects.get(email=email)
                user.is_active = is_active
                user.save()
                log_activity(request.user, "Toggle User Status", f"User: {user.email}", f"User {is_active}")
                return Response({
                    'message': f'User {user.email} has been {is_active}'
                }, status=status.HTTP_200_OK)
            except CustomUser.DoesNotExist:
                return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def reset_user_password(self, request):
        """Reset user's password"""
        serializer = UserEmailSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']
            
            try:
                user = CustomUser.objects.get(email=email)
                # Generate a random password
                new_password = CustomUser.objects.make_random_password()
                user.set_password(new_password)
                user.save()
                
                # TODO: Send email with new password
                
                log_activity(request.user, "Reset Password", f"User: {user.email}", "Password reset")
                return Response({
                    'message': f'Password has been reset for user {user.email}'
                }, status=status.HTTP_200_OK)
            except CustomUser.DoesNotExist:
                return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['delete'])
    def delete_user(self, request):
        """Delete a user"""
        serializer = UserEmailSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']
            
            try:
                user = CustomUser.objects.get(email=email)
                user_email = user.email
                user.delete()
                log_activity(request.user, "Delete User", f"User: {user_email}", "User deleted")
                return Response({
                    'message': f'User {user_email} has been deleted'
                }, status=status.HTTP_200_OK)
            except CustomUser.DoesNotExist:
                return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def generate_shared_access_token(self, request):
        """
        Generate a token for shared access with specific permissions.
        """
        if not request.user.is_authenticated:
            return Response({"error": "Authentication required"}, status=status.HTTP_401_UNAUTHORIZED)

        # Get the target user email and permissions from the request
        target_email = request.data.get('target_email')
        permissions = request.data.get('permissions', [])
        expires_in_days = request.data.get('expires_in_days', 7)  # Default 7 days

        if not target_email:
            return Response({"error": "Target email is required"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Get the target user
            target_user = CustomUser.objects.get(email=target_email)
            
            # Create the access token
            token = AccessToken.objects.create(
                created_by=request.user,
                granted_to=target_user,
                expires_at=timezone.now() + timedelta(days=expires_in_days)
            )

            # Add custom permissions if specified
            if permissions:
                custom_permissions = CustomPermission.objects.filter(codename__in=permissions)
                token.custom_permissions.set(custom_permissions)

            # Create a shared access record
            SharedAccess.objects.create(
                shared_by=request.user.profile,
                shared_with=target_user.profile
            )

            return Response({
                "token": token.token,
                "expires_at": token.expires_at,
                "permissions": [p.codename for p in token.custom_permissions.all()]
            })

        except CustomUser.DoesNotExist:
            return Response({"error": "Target user not found"}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    def manage_shared_access(self, request):
        """
        Manage shared access permissions between users.
        """
        try:
            target_email = request.data.get('target_email')
            permissions = request.data.get('permissions', [])
            action = request.data.get('action', 'grant')  # 'grant' or 'revoke'
            
            if not target_email:
                return Response(
                    {'error': 'Target email is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            try:
                target_user = CustomUser.objects.get(email=target_email)
            except CustomUser.DoesNotExist:
                return Response(
                    {'error': 'Target user not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
                
            if action == 'grant':
                # Create or update shared access
                shared_access, created = SharedAccess.objects.update_or_create(
                    granted_by=request.user,
                    granted_to=target_user,
                    defaults={
                        'permissions': permissions,
                        'is_active': True,
                        'expires_at': timezone.now() + timezone.timedelta(days=7)
                    }
                )
                
                # Generate access token
                access_token = AccessToken.objects.create(
                    token=secrets.token_urlsafe(32),
                    granted_by=request.user,
                    granted_to=target_user,
                    expires_at=shared_access.expires_at
                )
                
                # Add custom permissions
                for permission in permissions:
                    access_token.custom_permissions.add(permission)
                    
                return Response({
                    'message': 'Shared access granted successfully',
                    'access_token': access_token.token,
                    'expires_at': access_token.expires_at
                }, status=status.HTTP_200_OK)
                
            elif action == 'revoke':
                # Revoke shared access
                SharedAccess.objects.filter(
                    granted_by=request.user,
                    granted_to=target_user
                ).update(is_active=False)
                
                # Deactivate access tokens
                AccessToken.objects.filter(
                    granted_by=request.user,
                    granted_to=target_user
                ).update(is_active=False)
                
                return Response({
                    'message': 'Shared access revoked successfully'
                }, status=status.HTTP_200_OK)
                
            else:
                return Response(
                    {'error': 'Invalid action'},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class AvailableRolesView(viewsets.ReadOnlyModelViewSet):
    queryset = Role.objects.all()
    serializer_class = RoleSerializer
    permission_classes = [IsAdministrator, IsAuthenticated]

    def list(self, request):
        roles = self.get_queryset()
        return Response({'roles': [{'id': role.id, 'name': role.name} for role in roles]}) 