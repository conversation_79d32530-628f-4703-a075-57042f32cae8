"""
Views package for the roles app.
"""
# Role Management
from .role_management import RoleViewSet, AvailableRolesView

# Profile Management
from .profile_management import (
    UserProfileView, ProfileToggleView, ProfileFieldsVisibilityView,
    get_profile_categories
)

# Admin Management
from .admin_management import UserProfileAdminView, create_first_admin

# Community Management
from .community_management import (
    CommunityViewSet, CommunityCreateView, CommunityListView,
    CommunityDetailView, CommunityMembershipView
)

# Credential Management
from .credential_management import (
    DoctorCredentialSubmissionView, AdminCredentialVerificationView
)

# System Management
from .system_management import (
    ActivityLogView, ChangeLanguageView, ChangeLanguageAPIView
)

__all__ = [
    # Role Management
    'RoleViewSet',
    'AvailableRolesView',
    
    # Profile Management
    'UserProfileView',
    'ProfileToggleView', 
    'ProfileFieldsVisibilityView',
    'get_profile_categories',
    
    # Admin Management
    'UserProfileAdminView',
    'create_first_admin',
    
    # Community Management
    'CommunityViewSet',
    'CommunityCreateView',
    'CommunityListView',
    'CommunityDetailView',
    'CommunityMembershipView',
    
    # Credential Management
    'DoctorCredentialSubmissionView',
    'AdminCredentialVerificationView',
    
    # System Management
    'ActivityLogView',
    'ChangeLanguageView',
    'ChangeLanguageAPIView',
] 