from .mixins import *


class UserProfileView(APIView):
    permission_classes = []
    parser_classes = (MultiP<PERSON><PERSON><PERSON><PERSON>, Form<PERSON>arser, JSONParser)
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.translation_service = TranslationService()
    
    def get_object(self, user):
        try:
            return Profile.objects.get(user=user)
        except Profile.DoesNotExist:
            return Profile.objects.create(user=user)
    
    def get(self, request, pk=None):
        try:
            if pk:
                try:
                    user = CustomUser.objects.select_related('profile').get(id=pk)
                except CustomUser.DoesNotExist:
                    return Response(
                        {"error": "User not found"}, 
                        status=status.HTTP_404_NOT_FOUND
                    )

                profile = self.get_object(user)
                logger.info(f"login user is {request.user}")
                # Access rules:
                # - If profile is public: anyone can view
                # - If profile is not public: only the owner can view
                if profile.is_public_profile:
                    pass  # Public profile, allow access
                else:
                    # Not public: only allow if user is authenticated and is the owner
                    if not request.user.is_authenticated or request.user != profile.user:
                        return Response(
                            {"error": "This profile is private"}, 
                            status=status.HTTP_403_FORBIDDEN
                        )
            else:
                # No pk provided: only allow authenticated users to view their own profile
                if not request.user.is_authenticated:
                    return Response(
                        {"error": "Authentication required to view own profile"}, 
                        status=status.HTTP_401_UNAUTHORIZED
                    )
                profile = self.get_object(request.user)
            
            serializer = UserProfileSerializer(profile, context={'request': request})
            data = serializer.data
            
            # Handle profile pictures
            if profile.profile_picture:
                full_blob_name = f"UID_{profile.user.id}/profile_picture/{profile.profile_picture}"
                data['profile_picture'] = get_signed_url(full_blob_name, profile.user.id, timedelta(days=7))
            
            if profile.private_profile_picture and (request.user.is_authenticated and (request.user == profile.user or request.user.role.name == 'Administrator')):
                try:
                    data['private_profile_picture'] = get_private_profile_picture_signed_url(profile.private_profile_picture, profile.user.id)
                except Exception as e:
                    logger.error(f"Error generating signed URL for private profile picture: {str(e)}")
                    data['private_profile_picture'] = None
            
            # Background image is now handled by the custom serializer field
            # No need to manually process it here

            try:
                enterprise = None
                # Nếu là owner
                if hasattr(profile.user, "enterprise") and profile.user.enterprise:
                    enterprise = profile.user.enterprise
                    user_is_owner = request.user == enterprise.owner
                else:
                    # Nếu là member
                    member_obj = EnterpriseMember.objects.filter(user=profile.user, is_active=True).select_related("enterprise").first()
                    enterprise = member_obj.enterprise if member_obj else None
                    user_is_owner = False

                user_is_member = False
                if enterprise:
                    user_is_member = EnterpriseMember.objects.filter(enterprise=enterprise, user=request.user, is_active=True).exists()

                if enterprise and enterprise.logo and (user_is_owner or user_is_member):
                    data['enterprise_logo'] = get_enterprise_logo_signed_url(enterprise.logo, profile.user.id)
                else:
                    data['enterprise_logo'] = None
            except Exception as e:
                logger.error(f"Error getting enterprise logo: {str(e)}")
                data['enterprise_logo'] = None
            # Filter data based on visibility settings
            if profile.is_public_profile:
                if not profile.show_education:
                    data.pop('education', None)
                if not profile.show_research_papers:
                    data.pop('research_papers', None)
                if not profile.show_awards:
                    data.pop('awards', None)
                if not profile.show_youtube_videos:
                    data.pop('youtube_videos', None)
                if not profile.show_practice_locations:
                    data.pop('practice_locations', None)
                if not profile.show_credential_documents:
                    data.pop('credential_documents', None)
                if not profile.show_custom_information:
                    data.pop('custom_information', None)

            return Response(data)

        except Exception as e:
            logger.error(f"Error in get method: {str(e)}")
            return Response(
                {"error": str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get'])
    def get_profile_categories(self, request, pk=None):
        try:
            page = self.request.query_params.get('page', 1)
            page_size = self.request.query_params.get('page_size', 10)
            category_id = self.request.query_params.get('id', None)
            
            user = CustomUser.objects.select_related('profile').get(id=pk)

            if not user.profile.is_public_profile:
                return Response({"error": "This profile is private"}, status=status.HTTP_403_FORBIDDEN)
            # Get all categories with their related content
            queryset = ProfileCategory.objects.filter(
                user=user,
                hidden=False  # Only get non-hidden categories
            ).prefetch_related(
                'profilecategorycontent_set'  # Get all related content
            ).order_by('created_at')
            
            # Filter by ID if provided
            if category_id:
                queryset = queryset.filter(id=category_id)
            
            # Filter by name if provided
            name = self.request.query_params.get('name', None)
            if name:
                queryset = queryset.filter(name__icontains=name)
                
            paginated_queryset = paginate_queryset(queryset, page, page_size)
            serializer = self.get_serializer(paginated_queryset, many=True)
            
            # Add contents to each category in response
            response_data = serializer.data
            for category in response_data:
                category_contents = ProfileCategoryContent.objects.filter(
                    profile_category_id=category['id']
                ).values('id', 'content')
                category['contents'] = [
                    {'id': content['id'], 'content': content['content']} 
                    for content in category_contents
                ]
                
            return Response(response_data)
            
        except Exception as e:
            logger.error(f"Error listing profile categories: {str(e)}")
            return Response(
                {'error': 'Failed to retrieve profile categories'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def post(self, request):
        logger.info("Received POST request for UserProfileView")
        profile = self.get_object(request.user)
        serializer = UserProfileSerializer(profile, data=request.data, partial=True)
        if serializer.is_valid():
            if 'profile_picture' in request.FILES:
                file = request.FILES['profile_picture']
                try:
                    compressed_image = compress_image(file)
                    blob_name = upload_file_to_gcs(compressed_image, request.user.id, file_type='profile_picture')
                    profile.profile_picture = blob_name
                    profile.save()
                except Exception as e:
                    logger.error(f"Error uploading file: {str(e)}")
                    return Response({"error": "Failed to upload file"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                
            if 'private_profile_picture' in request.FILES:
                private_file = request.FILES['private_profile_picture']
                try:
                    compressed_private_image = compress_image(private_file)
                    private_blob_name = upload_file_to_gcs(compressed_private_image, request.user.id, file_type='private_profile_picture')
                    profile.private_profile_picture = private_blob_name
                    profile.save()
                except Exception as e:
                    logger.error(f"Error uploading file: {str(e)}") 
                    return Response({"error": "Failed to upload file"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            # Handle background_image upload with position/scale data
            if 'background_image' in request.FILES:
                logger.info(f"Processing background_image upload for user {request.user.id}")
                background_file = request.FILES['background_image']
                logger.info(f"Background file: {background_file.name}, size: {background_file.size}")
                try:
                    compressed_background_image = compress_image(background_file)
                    background_blob_name = upload_file_to_gcs(compressed_background_image, request.user.id, file_type='background_image')
                    logger.info(f"File uploaded to GCS: {background_blob_name}")
                    
                    # Get position/scale from request data
                    x = float(request.data.get('background_x', 0))
                    y = float(request.data.get('background_y', 0))
                    scale = float(request.data.get('background_scale', 1.0))
                    logger.info(f"Position/scale data: x={x}, y={y}, scale={scale}")
                    
                    # Validate scale (between 0.1 and 5.0)
                    scale = max(0.1, min(5.0, scale))
                    
                    # Always save as JSON structure for new uploads
                    background_data = {
                        'x': x,
                        'y': y,
                        'scale': scale,
                        'url': background_blob_name
                    }
                    # Temporary fix: serialize to JSON string for CharField
                    import json
                    profile.background_image = json.dumps(background_data)
                    logger.info(f"Saving background_image to profile: {background_data}")
                    logger.info(f"Serialized as JSON string: {profile.background_image}")
                    
                    profile.save()
                    logger.info(f"Profile saved successfully. Current background_image: {profile.background_image}")
                except Exception as e:
                    logger.error(f"Error uploading background image: {str(e)}") 
                    return Response({"error": "Failed to upload background image"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            # Handle position/scale update without file upload
            elif any(key in request.data for key in ['background_x', 'background_y', 'background_scale']):
                if profile.background_image:
                    try:
                        import json
                        # Get current values - handle both string and dict formats
                        if isinstance(profile.background_image, str):
                            try:
                                # Try to parse as JSON
                                current_data = json.loads(profile.background_image)
                                if isinstance(current_data, dict):
                                    current_url = current_data.get('url', '')
                                    current_x = current_data.get('x', 0)
                                    current_y = current_data.get('y', 0)
                                    current_scale = current_data.get('scale', 1.0)
                                else:
                                    # Old string blob name format
                                    current_url = profile.background_image
                                    current_x = current_y = 0
                                    current_scale = 1.0
                            except json.JSONDecodeError:
                                # Old string blob name format
                                current_url = profile.background_image
                                current_x = current_y = 0
                                current_scale = 1.0
                        elif isinstance(profile.background_image, dict):
                            current_url = profile.background_image.get('url', '')
                            current_x = profile.background_image.get('x', 0)
                            current_y = profile.background_image.get('y', 0)
                            current_scale = profile.background_image.get('scale', 1.0)
                        else:
                            # String format - convert to dict
                            current_url = str(profile.background_image)
                            current_x = current_y = 0
                            current_scale = 1.0
                        
                        # Update with new values
                        x = float(request.data.get('background_x', current_x))
                        y = float(request.data.get('background_y', current_y))
                        scale = float(request.data.get('background_scale', current_scale))
                        
                        # Validate scale
                        scale = max(0.1, min(5.0, scale))
                        
                        # Save as JSON structure
                        background_data = {
                            'x': x,
                            'y': y,
                            'scale': scale,
                            'url': current_url
                        }
                        profile.background_image = json.dumps(background_data)
                        profile.save()
                    except Exception as e:
                        logger.error(f"Error updating background image position: {str(e)}")
                        return Response({"error": "Failed to update background image position"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                
            try:
                with transaction.atomic():
                    # Save background_image value before serializer.save() if it was processed
                    background_image_backup = None
                    if 'background_image' in request.FILES or any(key in request.data for key in ['background_x', 'background_y', 'background_scale']):
                        background_image_backup = profile.background_image
                        logger.info(f"Backing up background_image before serializer.save(): {background_image_backup}")
                    
                    updated_profile = serializer.save()
                    logger.info(f"After serializer.save(), background_image value: {updated_profile.background_image}")
                    
                    # Restore background_image if it was processed manually
                    if background_image_backup is not None:
                        logger.info(f"Restoring background_image from backup: {background_image_backup}")
                        updated_profile.background_image = background_image_backup
                        updated_profile.save()
                        logger.info(f"After restore, background_image value: {updated_profile.background_image}")
                    
                    self.translation_service.invalidate_data(f'user_{profile.user.id}')
                return Response(UserProfileSerializer(updated_profile, context={'request': request}).data, status=status.HTTP_200_OK)
            except Exception as e:
                return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    put = post
    patch = post

    def handle_nested_data(self, data, profile):
        nested_fields = {
            'research_papers': (ResearchPaper, ResearchPaperSerializer),
            'awards': (Award, AwardSerializer),
            'youtube_videos': (YouTubeVideo, YouTubeVideoSerializer),
            'practice_locations': (PracticeLocation, PracticeLocationSerializer),
            'education': (Education, EducationSerializer),
        }

        for field, (model, serializer_class) in nested_fields.items():
            if field in data:
                self.update_nested_data(data[field], model, serializer_class, profile)

    def update_nested_data(self, items_data, model, serializer_class, profile):
        existing_items = model.objects.filter(profile=profile)
        existing_ids = set(existing_items.values_list('id', flat=True))
        updated_ids = set()

        for item_data in items_data:
            item_id = item_data.get('id')
            if item_id:
                if item_id in existing_ids:
                    item = existing_items.get(id=item_id)
                    serializer = serializer_class(item, data=item_data, partial=True)
                else:
                    continue  # Skip if ID is provided but doesn't exist
            else:
                serializer = serializer_class(data=item_data)

            if serializer.is_valid():
                serializer.save(profile=profile)
                if item_id:
                    updated_ids.add(item_id)
            else:
                # You might want to handle validation errors here
                pass

        # Delete items that weren't updated or created
        items_to_delete = existing_ids - updated_ids
        existing_items.filter(id__in=items_to_delete).delete()

    def delete(self, request):
        profile = self.get_object(request.user)
        
        # Get the picture type from the request body
        picture_type = request.data.get('type')
        
        if not picture_type:
            return Response({"error": "Picture type must be specified. Use 'public', 'private', or 'background'."}, status=status.HTTP_400_BAD_REQUEST)
        
        if picture_type not in ['public', 'private', 'background']:
            return Response({"error": "Invalid picture type. Use 'public', 'private', or 'background'."}, status=status.HTTP_400_BAD_REQUEST)
        
        if picture_type == 'public':
            picture_field = 'profile_picture'
        elif picture_type == 'private':
            picture_field = 'private_profile_picture'
        else:  # background
            picture_field = 'background_image'
        
        if not getattr(profile, picture_field):
            image_type = "profile picture" if picture_type in ['public', 'private'] else "background image"
            return Response({"message": f"No {picture_type} {image_type} to delete"}, status=status.HTTP_404_NOT_FOUND)
        
        try:
            client = storage.Client(project=settings.GS_PROJECT_ID, credentials=settings.GS_CREDENTIALS)
            bucket = client.get_bucket(settings.GS_BUCKET_NAME)
            
            # Handle different field formats
            field_value = getattr(profile, picture_field)
            if picture_type == 'background':
                # Handle JSON string format for background image
                if isinstance(field_value, str):
                    try:
                        import json
                        # Try to parse as JSON
                        parsed_data = json.loads(field_value)
                        if isinstance(parsed_data, dict):
                            blob_name = parsed_data.get('url', '')
                        else:
                            # Old string format
                            blob_name = field_value
                    except json.JSONDecodeError:
                        # Old string format
                        blob_name = field_value
                elif isinstance(field_value, dict):
                    # Direct dict format (unlikely with CharField)
                    blob_name = field_value.get('url', '')
                else:
                    blob_name = str(field_value)
                    
                if not blob_name:
                    return Response({"error": "No background image URL found"}, status=status.HTTP_400_BAD_REQUEST)
                file_name = blob_name.split('/')[-1]
            else:
                # String format for other images
                file_name = field_value.split('/')[-1]
            
            if picture_type == 'public':
                folder = 'profile_picture'
            elif picture_type == 'private':
                folder = 'private_profile_picture'
            else:  # background
                folder = 'background_image'
            full_blob_name = f"UID_{request.user.id}/{folder}/{file_name}"
            
            blob = bucket.blob(full_blob_name)
            
            try:
                blob.delete()
                logger.info(f"Deleted blob: {full_blob_name}")
            except NotFound:
                logger.warning(f"Blob not found: {full_blob_name}")
            
            # Clear the field appropriately
            if picture_type == 'background':
                setattr(profile, picture_field, '')  # Empty string for background (CharField)
            else:
                setattr(profile, picture_field, None)  # None for other fields
            profile.save()
            image_type = "profile picture" if picture_type in ['public', 'private'] else "background image"
            return Response({"message": f"{picture_type.capitalize()} {image_type} deleted successfully"}, status=status.HTTP_200_OK)
        except Exception as e:
            image_type = "profile picture" if picture_type in ['public', 'private'] else "background image"
            logger.error(f"Error deleting {picture_type} {image_type}: {str(e)}")
            return Response({"error": f"Failed to delete {picture_type} {image_type}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def get_profile_categories(request, pk=None):
    try:
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 10)
        category_id = request.query_params.get('id', None)
            
        user = CustomUser.objects.select_related('profile').get(id=pk)

        if not user.profile.is_public_profile:
            return Response({"error": "This profile is private"}, status=status.HTTP_403_FORBIDDEN)
        # Get all categories with their related content
        queryset = ProfileCategory.objects.filter(
            user=user,
            hidden=False  # Only get non-hidden categories
        ).prefetch_related(
            'profilecategorycontent_set'  # Get all related content
        ).order_by('created_at')
        
        # Filter by ID if provided
        if category_id:
            queryset = queryset.filter(id=category_id)
        
        # Filter by name if provided
        name = request.query_params.get('name', None)
        if name:
            queryset = queryset.filter(name__icontains=name)
            
        paginated_queryset = paginate_queryset(queryset, page, page_size)
        serializer = ProfileCategorySerializer(paginated_queryset, many=True)
        
        # Add contents to each category in response
        response_data = serializer.data
        for category in response_data:
            category_contents = ProfileCategoryContent.objects.filter(
                profile_category_id=category['id']
            ).values('id', 'content')
            category['contents'] = [
                {'id': content['id'], 'content': content['content']} 
                for content in category_contents
            ]
            
        return Response(response_data)
        
    except Exception as e:
        logger.error(f"Error listing profile categories: {str(e)}")
        return Response(
            {'error': 'Failed to retrieve profile categories'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


class ProfileToggleView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        profile = request.user.profile
        serializer = ProfileToggleSerializer(profile)
        return Response(serializer.data)

    def put(self, request):
        profile = request.user.profile
        logger.info(f"Received data: {request.data}")
        logger.info(f"Current is_public_profile value: {profile.is_public_profile}")
        
        serializer = ProfileToggleSerializer(profile, data=request.data, partial=True)
        if serializer.is_valid():
            updated_profile = serializer.save()
            logger.info(f"Updated is_public_profile value: {updated_profile.is_public_profile}")
            return Response(serializer.data)
        logger.error(f"Serializer errors: {serializer.errors}")
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ProfileFieldsVisibilityView(generics.RetrieveUpdateAPIView):
    serializer_class = ProfileFieldsVisibilitySerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        return self.request.user.profile

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        return Response(serializer.data) 