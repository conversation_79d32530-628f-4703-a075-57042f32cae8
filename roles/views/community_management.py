from .mixins import *


class CommunityViewSet(viewsets.ModelViewSet):
    queryset = Community.objects.all()
    serializer_class = CommunitySerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save()
        community = serializer.save(creator=self.request.user)
        log_activity(self.request.user, "Created Community", f"Community: {community.name}")
        
    def perform_update(self, serializer):
        community = serializer.save()
        log_activity(self.request.user, "Updated Community", f"Community: {community.name}")
        
    def perform_destroy(self, instance):
        log_activity(self.request.user, "Deleted Community", f"Community: {instance.name}")
        instance.delete()
        
    def join_community(self, request, pk=None):
        community = self.get_object()
        community.members.add(request.user)
        log_activity(request.user, "Joined Community", f"Community: {community.name}")
        return Response(status=status.HTTP_204_NO_CONTENT)

    def leave_community(self, request, pk=None):
        community = self.get_object()
        community.members.remove(request.user)
        log_activity(request.user, "Left Community", f"Community: {community.name}")
        return Response(status=status.HTTP_204_NO_CONTENT)

    @action(detail=False, methods=['get'], url_path='search')
    def search_communities(self, request):
        query = request.query_params.get("q", "")
        if query:
            # Combine exact, case-insensitive, and similarity searches
            communities = Community.objects.annotate(
                similarity=TrigramSimilarity('name', query) + TrigramSimilarity('description', query)
            ).filter(
                Q(name__iexact=query) |  # Exact match (case-insensitive)
                Q(name__icontains=query) |  # Partial match (case-insensitive)
                Q(description__icontains=query) |  # Partial match in description
                Q(similarity__gt=0.3)  # Similar names or descriptions
            ).order_by('-similarity', 'name')  # Order by similarity, then name

            serializer = self.get_serializer(communities, many=True)
            return Response(serializer.data)
        return Response({"detail": "Please provide a search query."}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'], permission_classes=[IsAdminOrModerator])
    def flag_post(self, request, pk=None):
        post = self.get_object()
        post.is_flagged = True
        post.save()
        return Response({"message": "Post has been flagged"}, status=status.HTTP_200_OK)

    @action(detail=True, methods=['delete'], permission_classes=[IsAdminOrModerator])
    def delete_post(self, request, pk=None):
        post = self.get_object()
        post.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class CommunityCreateView(generics.CreateAPIView):
    queryset = Community.objects.all()
    serializer_class = CommunitySerializer
    permission_classes = [CanCreateCommunity]

    def perform_create(self, serializer):
        serializer.save(creator=self.request.user)
        log_activity(self.request.user, "Created Community", f"Community: {serializer.instance.name}")


class CommunityListView(generics.ListAPIView):
    queryset = Community.objects.all()
    serializer_class = CommunitySerializer
    permission_classes = [permissions.IsAuthenticated]


class CommunityDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Community.objects.all()
    serializer_class = CommunitySerializer
    permission_classes = [permissions.IsAuthenticated]

    def update(self, request, *args, **kwargs):
        if not IsAdminOrModerator().has_permission(request, self):
            return Response({"error": "Only Admin or moderators can update communities"}, status=status.HTTP_403_FORBIDDEN)
        return super().update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        if not IsAdministrator().has_permission(request, self):
            return Response({"error": "Only Admin can delete communities"}, status=status.HTTP_403_FORBIDDEN)
        return super().destroy(request, *args, **kwargs)


class CommunityMembershipView(generics.UpdateAPIView):
    queryset = Community.objects.all()
    serializer_class = CommunitySerializer
    permission_classes = [IsAdminOrModerator]

    def update(self, request, *args, **kwargs):
        community = self.get_object()
        user_id = request.data.get('user_id')
        action = request.data.get('action')

        if not user_id or not action:
            return Response({"error": "Both user_id and action are required"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = CustomUser.objects.get(id=user_id)
        except CustomUser.DoesNotExist:
            return Response({"error": "User not found"}, status=status.HTTP_404_NOT_FOUND)

        if action == 'add':
            community.members.add(user)
        elif action == 'remove':
            community.members.remove(user)
        else:
            return Response({"error": "Invalid action. Use 'add' or 'remove'"}, status=status.HTTP_400_BAD_REQUEST)

        return Response({"message": f"User {action}ed to/from the community successfully"}) 