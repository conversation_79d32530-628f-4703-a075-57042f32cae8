from .mixins import *


class DoctorCredentialSubmissionView(generics.CreateAPIView):
    queryset = Profile.objects.all()
    serializer_class = CredentialVerificationSerializer
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser)

    def perform_create(self, serializer):
        profile = self.request.user.profile
        instance = serializer.save()
        
        # Get the newly created documents
        documents = CredentialDocument.objects.filter(profile=profile).order_by('-uploaded_at')[:len(serializer.validated_data['credential_documents'])]
        
        # Notify administrators about the new submission
        send_credential_submission_email(profile, documents)

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)


class AdminCredentialVerificationView(generics.UpdateAPIView):
    queryset = Profile.objects.all()
    serializer_class = CredentialVerificationSerializer
    permission_classes = [IsAdministrator]
    lookup_field = 'user__email'

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        
        # Update verification status and notes
        instance.is_credentials_verified = serializer.validated_data.get('is_credentials_verified', instance.is_credentials_verified)
        instance.credential_verification_notes = serializer.validated_data.get('credential_verification_notes', instance.credential_verification_notes)
        instance.save()

        # If credentials are verified, send email to the doctor
        if instance.is_credentials_verified:
            send_credential_verification_email(instance.user)

        return Response(serializer.data) 