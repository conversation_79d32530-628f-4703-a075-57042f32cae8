"""
Refactored base.py - All views have been split into separate modules for better maintainability.

This file now only imports and re-exports all views from the modularized files.
For backward compatibility, all views are still accessible through this module.
"""

# Import all views from the split modules
from .role_management import RoleViewSet, AvailableRolesView
from .profile_management import (
    UserProfileView, ProfileToggleView, ProfileFieldsVisibilityView,
    get_profile_categories
)
from .admin_management import UserProfileAdminView, create_first_admin
from .community_management import (
    CommunityViewSet, CommunityCreateView, CommunityListView,
    CommunityDetailView, CommunityMembershipView
)
from .credential_management import (
    DoctorCredentialSubmissionView, AdminCredentialVerificationView
)
from .system_management import (
    ActivityLogView, ChangeLanguageView, ChangeLanguageAPIView
)

# For backward compatibility, export all views
__all__ = [
    # Role Management
    'RoleViewSet',
    'AvailableRolesView',
    
    # Profile Management
    'UserProfileView',
    'ProfileToggleView', 
    'ProfileFieldsVisibilityView',
    'get_profile_categories',
    
    # Admin Management
    'UserProfileAdminView',
    'create_first_admin',
    
    # Community Management
    'CommunityViewSet',
    'CommunityCreateView',
    'CommunityListView',
    'CommunityDetailView',
    'CommunityMembershipView',
    
    # Credential Management
    'DoctorCredentialSubmissionView',
    'AdminCredentialVerificationView',
    
    # System Management
    'ActivityLogView',
    'ChangeLanguageView',
    'ChangeLanguageAPIView',
]

# All classes have been moved to separate module files
# This file now only serves as an import hub for backward compatibility