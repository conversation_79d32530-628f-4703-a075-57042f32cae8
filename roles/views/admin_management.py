from .mixins import *


class UserProfileAdminView(generics.RetrieveUpdateAPIView):
    queryset = Profile.objects.all()
    serializer_class = UserProfileAdminSerializer
    permission_classes = [IsAdministrator, IsAuthenticated]
    lookup_field = 'user__email'  # Use the user's email as the lookup field
    http_method_names = ['get', 'post', 'put', 'patch']  # Add 'post' here

    def get_object(self):
        email = self.kwargs.get('user__email')
        user = get_object_or_404(CustomUser, email=email)
        return get_object_or_404(Profile, user=user)

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, '_prefetched_objects_cache', None):
            # If 'prefetch_related' has been applied to a queryset, we need to
            # forcibly invalidate the prefetch cache on the instance.
            instance._prefetched_objects_cache = {}

        return Response(serializer.data)

    def partial_update(self, request, *args, **kwargs):
        kwargs['partial'] = True
        return self.update(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        return self.partial_update(request, *args, **kwargs)

    @action(detail=True, methods=['post'])
    def verify_credentials(self, request, user__email=None):
        profile = self.get_object()
        profile.is_credentials_verified = True
        profile.save()
        serializer = self.get_serializer(profile)
        return Response(serializer.data)


@api_view(['POST'])
@permission_classes([AllowAny])
def create_first_admin(request):
    """
    Special endpoint to create the first admin user.
    This endpoint should only be used once to create the initial admin user.
    After that, use the regular admin management endpoints.
    """
    # Check if there are any users with admin role
    if CustomUser.objects.filter(role__name='Admin').exists():
        return Response(
            {"error": "Admin users already exist. Use the regular admin management endpoints."},
            status=status.HTTP_400_BAD_REQUEST
        )

    serializer = FirstAdminUserSerializer(data=request.data)
    if serializer.is_valid():
        try:
            # Create the user with email verified
            user = CustomUser.objects.create_user(
                email=serializer.validated_data['email'],
                password=serializer.validated_data['password'],
                first_name=serializer.validated_data.get('first_name', ''),
                last_name=serializer.validated_data.get('last_name', ''),
                is_email_verified=True  # Set email as verified
            )

            # Assign admin role
            admin_role = Role.objects.get(name='Admin')
            user.role = admin_role
            user.save()

            # Log the activity
            log_activity(user, "Created First Admin", f"User: {user.email}", "First admin user created")

            return Response({
                "message": "First admin user created successfully",
                "user": {
                    "email": user.email,
                    "first_name": user.first_name,
                    "last_name": user.last_name
                }
            }, status=status.HTTP_201_CREATED)
        except Role.DoesNotExist:
            return Response(
                {"error": "Admin role not found. Please run migrations and create default roles."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST) 