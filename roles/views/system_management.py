from .mixins import *


class ActivityLogView(generics.ListAPIView):
    queryset = ActivityLog.objects.all().order_by('-timestamp')
    serializer_class = ActivityLogSerializer
    permission_classes = [IsAuthenticated, IsAdministrator]
    pagination_class = PageNumberPagination
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        user_email = self.request.query_params.get('user_email')
        action = self.request.query_params.get('action')
        role = self.request.query_params.get('role')
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        
        if user_email:
            queryset = queryset.filter(user__email=user_email)
        if action:
            queryset = queryset.filter(action__icontains=action)
        if role:
            queryset = queryset.filter(role=role)
        if start_date and end_date:
            queryset = queryset.filter(timestamp__range=[start_date, end_date])
        return queryset
    
    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        total_logs = ActivityLog.objects.count()
        filtered_logs = queryset.count()
        
        if filtered_logs == 0:
            return JsonResponse({
                "message": "No logs found",
                "total_logs_in_database": total_logs,
                "filters_applied": bool(request.query_params),
                "applied_filters": dict(request.query_params)
            })
        
        return super().list(request, *args, **kwargs)


class ChangeLanguageView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        language = request.data.get('language')
        if language not in dict(settings.LANGUAGES).keys():
            return JsonResponse({'error': 'Invalid language code'}, status=400)

        translation.activate(language)
        request.session[settings.LANGUAGE_SESSION_KEY] = language
        request.session.modified = True

        return JsonResponse({
            'message': f'Language changed to {language}',
            'source_language': translation.get_language(),
            'session_language': request.session.get(settings.LANGUAGE_SESSION_KEY),
            'activated_language': translation.get_language()
        })


class ChangeLanguageAPIView(APIView):
    def post(self, request):
        lang = request.data.get('lang')
        if lang in dict(settings.LANGUAGES).keys():
            translation.activate(lang)
            request.session[translation.LANGUAGE_SESSION_KEY] = lang
            if request.user.is_authenticated:
                request.user.preferred_language = lang
                request.user.save()
            return Response({"message": f"Language changed to {lang}"}, status=status.HTTP_200_OK)
        return Response({"error": "Invalid language"}, status=status.HTTP_400_BAD_REQUEST) 